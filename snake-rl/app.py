#!/usr/bin/env python3
"""
Clean, simple web app for Snake RL
"""

from flask import Flask, jsonify, send_from_directory
from threading import Thread, Event
import numpy as np
import os
import time
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)

# Import game components
from snake_game import <PERSON><PERSON><PERSON>
from agent import RLAgent
from evaluation import Evaluator
from config import Config

app = Flask(__name__, static_folder='web/static', static_url_path='')

# Global variables
trainer = None
training_thread = None
stop_training = Event()

class SimpleTrainer:
    def __init__(self):
        self.env = SnakeGame()
        self.agent = RLAgent(state_size=(10, 10), action_size=3)

        # Try to load existing model using config
        self.model_path = Config.MODEL_PATH  # Use config path
        self._update_model_loaded_status()

        # Training stats
        self.episode = 0
        self.scores = []
        self.is_training = False

        # Store initial episodes_trained for correct calculation
        self.agent._initial_episodes_trained = self.agent.episodes_trained

    def _update_model_loaded_status(self):
        """Update model loaded status based on current state"""
        model_loaded = False

        if os.path.exists(self.model_path):
            if self.agent.load(self.model_path):
                self.model_loaded_from = self.model_path
                logging.info(f"Loaded model from {self.model_path}")
                model_loaded = True
            else:
                logging.warning(f"Failed to load model from {self.model_path}")

        if not model_loaded:
            self.model_loaded_from = "fresh_start"
            logging.info("No existing model found, starting fresh")

    def _check_model_status(self):
        """Check if model status should be updated (without reloading)"""
        if self.model_loaded_from == "fresh_start" and os.path.exists(self.model_path):
            # Model file exists but status shows fresh_start - update status
            self.model_loaded_from = self.model_path
            logging.info(f"Model status updated to: {self.model_path}")

    def train(self):
        """Simple training loop"""
        self.is_training = True
        logging.info("Starting training...")

        # Use the stored initial episodes_trained value
        initial_episodes_trained = self.agent._initial_episodes_trained
        session_episodes = 0  # Track episodes in this session only
        target_episodes = Config.EPISODES

        while not stop_training.is_set() and session_episodes < target_episodes:
            state = self.env.reset()
            done = False

            while not done and not stop_training.is_set():
                action = self.agent.act(state)
                next_state, reward, done, info = self.env.step(action)
                self.agent.remember(state, action, reward, next_state, done)

                if len(self.agent.memory) > Config.BATCH_SIZE:
                    self.agent.replay(Config.BATCH_SIZE)

                state = next_state

            # Track game score and update high score
            game_score = self.env.score
            self.scores.append(game_score)
            session_episodes += 1  # Increment session episodes
            self.episode = session_episodes  # Update display episode for API

            # Track high score in agent's training metrics
            if 'high_score' not in self.agent.training_metrics:
                self.agent.training_metrics['high_score'] = 0
            if game_score > self.agent.training_metrics['high_score']:
                self.agent.training_metrics['high_score'] = game_score

            # Save periodically (but don't update episodes_trained yet)
            if session_episodes % 50 == 0:
                self.agent.save(self.model_path)
                logging.info(f"Session Episode {session_episodes}, Score: {game_score}, High Score: {self.agent.training_metrics['high_score']}")

        # Final save - NOW update episodes_trained with the session episodes
        if session_episodes > 0:
            self.agent.episodes_trained = initial_episodes_trained + session_episodes
        self.agent.save(self.model_path)

        # Update model loaded status after saving (now we have a real model file)
        if session_episodes > 0 and self.model_loaded_from == "fresh_start":
            self.model_loaded_from = self.model_path
            logging.info(f"Model status updated to: {self.model_path}")

        self.is_training = False
        logging.info(f"Training completed. Episodes this session: {session_episodes}, Total model episodes: {self.agent.episodes_trained}")

    def reset_training(self):
        """Reset training to start fresh"""
        self.episode = 0
        self.scores = []
        self.model_loaded_from = "fresh_start"
        # Create new agent (fresh Q-table)
        self.agent = RLAgent(state_size=(10, 10), action_size=3)
        # Reset episodes_trained and high_score for fresh start
        self.agent.episodes_trained = 0
        self.agent.training_metrics['high_score'] = 0
        # Reset initial episodes_trained for correct calculation
        self.agent._initial_episodes_trained = 0
        # Remove existing model file to ensure fresh start
        if os.path.exists(self.model_path):
            os.remove(self.model_path)
            logging.info(f"Removed existing model file: {self.model_path}")
        logging.info("Training reset - starting with fresh model")

def get_trainer():
    global trainer
    if trainer is None:
        trainer = SimpleTrainer()
    return trainer

@app.route('/')
def index():
    return send_from_directory('web/static', 'index.html')

@app.route('/app.js')
def app_js():
    return send_from_directory('web/static', 'app.js')

@app.route('/api/status')
def status():
    trainer = get_trainer()
    return jsonify({
        'status': 'ok',
        'model_loaded': trainer.model_loaded_from != "fresh_start",
        'model_loaded_from': trainer.model_loaded_from,
        'q_table_shape': list(trainer.agent.q_table.shape),
        'memory_size': len(trainer.agent.memory),
        'epsilon': float(trainer.agent.epsilon),
        'episodes_trained': trainer.agent.episodes_trained
    })

@app.route('/api/training/status')
def training_status():
    trainer = get_trainer()
    global training_thread

    # Check if model status needs updating
    trainer._check_model_status()

    is_running = training_thread is not None and training_thread.is_alive()

    # Calculate high score
    high_score = 0
    if hasattr(trainer.agent, 'training_metrics') and 'high_score' in trainer.agent.training_metrics:
        high_score = trainer.agent.training_metrics.get('high_score', 0)
    if trainer.scores:
        session_high = max(trainer.scores)
        high_score = max(high_score, session_high)

    # Calculate episodes correctly
    episodes_trained = getattr(trainer.agent, 'episodes_trained', 0)  # Episodes from loaded model
    total_episodes = episodes_trained + trainer.episode  # Total episodes including current session

    return jsonify({
        'running': is_running,
        'episode': trainer.episode,
        'total_episodes': total_episodes,
        'score': trainer.scores[-1] if trainer.scores else 0,
        'avg_score': float(np.mean(trainer.scores[-10:])) if trainer.scores else 0,
        'high_score': high_score,
        'epsilon': float(trainer.agent.epsilon),
        'memory_size': len(trainer.agent.memory),
        'episodes_trained': episodes_trained,
        'completed': trainer.episode >= Config.EPISODES and not is_running,
        'model_loaded_from': trainer.model_loaded_from
    })

@app.route('/api/training/start', methods=['POST'])
def start_training():
    global training_thread
    trainer = get_trainer()

    if training_thread is None or not training_thread.is_alive():
        stop_training.clear()
        training_thread = Thread(target=trainer.train)
        training_thread.start()
        return jsonify({'status': 'training started'})
    else:
        return jsonify({'status': 'training already running'})

@app.route('/api/training/stop', methods=['POST'])
def stop_training_endpoint():
    stop_training.set()
    return jsonify({'status': 'training stopped'})

@app.route('/api/training/reset', methods=['POST'])
def reset_training_endpoint():
    """Reset training to start fresh"""
    global training_thread
    trainer = get_trainer()

    # Stop current training if running
    if training_thread is not None and training_thread.is_alive():
        stop_training.set()
        training_thread.join(timeout=2)  # Wait for thread to finish

    # Reset trainer
    trainer.reset_training()
    training_thread = None

    return jsonify({'status': 'training reset', 'episode': trainer.episode})

@app.route('/api/evaluation')
def evaluation():
    trainer = get_trainer()

    if not os.path.exists(trainer.model_path):
        return jsonify({'error': 'No model available'}), 404

    try:
        evaluator = Evaluator(trainer.model_path)
        results = evaluator.evaluate(num_episodes=20, render=False)

        return jsonify({
            'mean_score': float(results['mean_score']),
            'max_score': int(results['max_score']),
            'mean_steps': float(results['mean_steps']),
            'action_distribution': {
                'left': float(results['action_distribution']['left']),
                'right': float(results['action_distribution']['right']),
                'straight': float(results['action_distribution']['straight'])
            }
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/final_evaluation')
def final_evaluation():
    """Compatibility endpoint for frontend"""
    trainer = get_trainer()

    if not os.path.exists(trainer.model_path):
        return jsonify({'error': 'No model available'}), 404

    try:
        evaluator = Evaluator(trainer.model_path)
        results = evaluator.evaluate(num_episodes=20, render=False)

        # Format for frontend compatibility
        return jsonify({
            'evaluation': {
                'mean_score': float(results['mean_score']),
                'max_score': int(results['max_score']),
                'steps': float(results['mean_steps']),
                'action_distribution': {
                    'left': float(results['action_distribution']['left']),
                    'right': float(results['action_distribution']['right']),
                    'straight': float(results['action_distribution']['straight'])
                }
            }
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/final_replay')
def final_replay():
    """Compatibility endpoint for frontend"""
    return jsonify({'error': 'Replay functionality not implemented in clean version'}), 404

@app.route('/api/test')
def test():
    return jsonify({
        'message': 'Snake RL API is working!',
        'config': {
            'episodes': Config.EPISODES,
            'batch_size': Config.BATCH_SIZE,
            'grid_size': Config.GRID_SIZE,
            'memory_size': Config.MEMORY_SIZE
        }
    })

if __name__ == '__main__':
    # Create models directory
    os.makedirs('models', exist_ok=True)

    print("Starting Snake RL Web App...")
    print("Access at: http://localhost:5000")

    app.run(host='0.0.0.0', port=5000, debug=False)
