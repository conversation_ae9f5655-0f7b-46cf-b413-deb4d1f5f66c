# Snake RL Configuration - Cleaned and Updated
class Config:
    # ===== TRAINING PARAMETERS =====
    EPISODES = 1000              # Training episodes per session
    BATCH_SIZE = 32              # Experience replay batch size
    SAVE_INTERVAL = 100          # Episodes between model saves

    # ===== AGENT HYPERPARAMETERS =====
    # Q-Learning Parameters
    GAMMA = 0.99                 # Discount factor
    EPSILON_START = 1.0          # Initial exploration rate
    EPSILON_MIN = 0.01           # Minimum exploration rate
    EPSILON_DECAY = 0.995        # Exploration decay rate
    LEARNING_RATE = 0.001        # Q-learning rate
    MEMORY_SIZE = 10000          # Experience buffer size

    # Learning Rate Scheduling
    USE_LR_SCHEDULING = True
    LR_DECAY_RATE = 0.99
    LR_DECAY_INTERVAL = 100      # Episodes between decay
    LR_MIN = 0.0001

    # ===== STATE REPRESENTATION =====
    GRID_SIZE = 10               # Game grid size (10x10)
    STATE_DIMENSIONS = (10, 10, 4, 3, 3)  # (x, y, food_direction, danger_level, action)

    # ===== REWARD SYSTEM =====
    # Base Rewards
    REWARD_FOOD = 100            # Food reward
    REWARD_DEATH = -100          # Death penalty
    REWARD_STEP = -1             # Step penalty (encourages efficiency)

    # Dynamic Rewards
    USE_DYNAMIC_REWARDS = True
    REWARD_CLOSER = 2            # Moving closer to food
    REWARD_FARTHER = -2          # Moving away from food
    REWARD_SURVIVAL = 0.1        # Small survival bonus per step
    REWARD_LENGTH_MULTIPLIER = 2 # Multiply food reward by snake length

    # ===== ENVIRONMENT SETTINGS =====
    MAX_STEPS = 200              # Maximum steps per episode

    # ===== MODEL MANAGEMENT =====
    MODEL_DIR = "models"
    MODEL_PATH = "models/snake_agent_v2.npy"  # Primary model path

    # Backup Settings
    AUTO_BACKUP = True
    BACKUP_INTERVAL = 500        # Episodes between backups
    MAX_BACKUPS = 5              # Maximum backup files to keep

    # ===== TRAINING OPTIMIZATION =====
    # Early Stopping
    USE_EARLY_STOPPING = True
    EARLY_STOP_PATIENCE = 100    # Episodes without improvement
    EARLY_STOP_MIN_DELTA = 0.1   # Minimum improvement threshold

    # Performance Tracking
    METRICS_WINDOW = 100         # Episodes for moving averages