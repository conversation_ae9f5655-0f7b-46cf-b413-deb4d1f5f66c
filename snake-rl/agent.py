import numpy as np
from typing import Dict, Any, Tuple, List
from collections import deque
import random
import os
import logging

class RLAgent:
    """Enhanced Reinforcement Learning Agent for Snake Game"""

    def __init__(self, state_size: tuple, action_size: int):
        # Import config locally to avoid circular imports
        import config
        Config = config.Config
        self.config = Config
        self.state_size = (Config.GRID_SIZE, Config.GRID_SIZE)
        self.action_size = action_size
        self.direction = (0, 1)  # Initial direction (right)

        # Hyperparameters from config
        self.gamma = Config.GAMMA
        self.epsilon = Config.EPSILON_START
        self.epsilon_min = Config.EPSILON_MIN
        self.epsilon_decay = Config.EPSILON_DECAY
        self.learning_rate = Config.LEARNING_RATE
        self.initial_learning_rate = Config.LEARNING_RATE

        # Experience replay memory
        self.memory = deque(maxlen=Config.MEMORY_SIZE)
        # Track last actions to balance exploration
        self.last_actions = deque(maxlen=10)

        # Standard Q-table (optimized and stable)
        self.q_table = np.zeros(Config.STATE_DIMENSIONS, dtype=np.float16)

        # Learning rate scheduling
        self.use_lr_scheduling = Config.USE_LR_SCHEDULING
        self.lr_decay_rate = Config.LR_DECAY_RATE
        self.lr_decay_interval = Config.LR_DECAY_INTERVAL
        self.lr_min = Config.LR_MIN
        self.episodes_trained = 0

        # Performance tracking
        self.training_metrics = {
            'losses': [],
            'q_values': [],
            'exploration_rates': [],
            'learning_rates': [],
            'high_score': 0
        }

    def remember(self, state: np.ndarray, action: int, reward: float, next_state: np.ndarray, done: bool):
        """Store experience in memory with optional prioritization"""
        experience = (state, action, reward, next_state, done)
        self.memory.append(experience)



    def act(self, state: np.ndarray) -> int:
        """Select action using epsilon-greedy policy with balanced exploration"""
        if np.random.rand() <= self.epsilon:
            # Enhanced exploration with action balancing
            action = random.randrange(self.action_size)
            if len(self.last_actions) > 5:
                # Reduce probability of repeating dominant action
                action_counts = [self.last_actions.count(a) for a in range(3)]
                if max(action_counts) > len(self.last_actions) * 0.6:  # If one action dominates
                    # Choose less frequent action
                    min_count = min(action_counts)
                    action = action_counts.index(min_count)

            self.last_actions.append(action)
            return action

        # Exploitation with state features
        state_features = self._get_state_features(state)
        q_values = self.q_table[tuple(state_features)]
        action = np.argmax(q_values)

        # Track Q-values for monitoring
        self.training_metrics['q_values'].append(np.max(q_values))

        self.last_actions.append(action)
        return action

    def replay(self, batch_size: int) -> float:
        """Training with experience replay"""
        if len(self.memory) < batch_size:
            return 0.0

        # Sample experiences
        minibatch = random.sample(self.memory, batch_size)

        total_loss = 0.0

        for state, action, reward, next_state, done in minibatch:
            # Get state representations
            state_features = self._get_state_features(state)
            next_features = self._get_state_features(next_state)

            # Calculate target
            target = reward
            if not done:
                target += self.gamma * np.max(self.q_table[tuple(next_features)])

            # Current Q-value
            current_q = self.q_table[tuple(state_features) + (action,)]

            # TD error
            td_error = target - current_q

            # Update Q-value
            self.q_table[tuple(state_features) + (action,)] += \
                self.learning_rate * td_error

            total_loss += abs(td_error)

        # Update learning parameters
        self._update_learning_parameters()

        # Track basic metrics
        self.training_metrics['losses'].append(total_loss / batch_size)
        self.training_metrics['exploration_rates'].append(self.epsilon)
        self.training_metrics['learning_rates'].append(self.learning_rate)

        return total_loss / batch_size

    def _get_state_features(self, state: np.ndarray) -> tuple:
        """Get simplified state features"""
        return self._get_simplified_state(state)





    def _get_simplified_state(self, state: np.ndarray) -> tuple:
        """Standard state representation"""
        # Find positions
        head_pos = np.unravel_index(np.argmax(state == 1.0), state.shape)
        food_pos = np.unravel_index(np.argmax(state == -1.0), state.shape)

        # Convert coordinates with bounds checking
        head_x, head_y = min(max(head_pos[0], 0), 9), min(max(head_pos[1], 0), 9)
        food_x, food_y = min(max(food_pos[0], 0), 9), min(max(food_pos[1], 0), 9)

        # Food direction quadrant (0-3)
        if food_x < head_x and food_y <= head_y:
            food_dir = 0  # Top-left
        elif food_x >= head_x and food_y < head_y:
            food_dir = 1  # Top-right
        elif food_x < head_x and food_y > head_y:
            food_dir = 2  # Bottom-left
        else:
            food_dir = 3  # Bottom-right

        # Danger detection (0=none, 1=near, 2=collision)
        danger = 0
        if self._check_immediate_danger(state, head_pos):
            danger = 2
        elif self._check_near_danger(state, head_pos):
            danger = 1

        return (head_x, head_y, food_dir, danger)

    def _check_immediate_danger(self, state: np.ndarray, head_pos: tuple) -> bool:
        """Check for immediate collision danger"""
        x, y = head_pos
        # Check walls
        if (self.direction == (0, 1) and y == 9) or \
           (self.direction == (0, -1) and y == 0) or \
           (self.direction == (1, 0) and x == 9) or \
           (self.direction == (-1, 0) and x == 0):
            return True
        # Check body
        next_pos = (x + self.direction[0], y + self.direction[1])
        return state[next_pos] == 0.5

    def _check_near_danger(self, state: np.ndarray, head_pos: tuple) -> bool:
        """Check for nearby danger (1 step away)"""
        x, y = head_pos
        # Check if near walls
        if (self.direction == (0, 1) and y >= 8) or \
           (self.direction == (0, -1) and y <= 1) or \
           (self.direction == (1, 0) and x >= 8) or \
           (self.direction == (-1, 0) and x <= 1):
            return True
        # Check if body is nearby
        next_pos = (x + self.direction[0], y + self.direction[1])
        return state[next_pos] == 0.5

    def _check_body_ahead(self, state: np.ndarray, head_pos: tuple) -> int:
        """Check if body is in front of head"""
        x, y = head_pos
        if self.direction == (0, 1):  # Right
            return 1 if y < 9 and state[x, y+1] == 0.5 else 0
        elif self.direction == (0, -1):  # Left
            return 1 if y > 0 and state[x, y-1] == 0.5 else 0
        elif self.direction == (1, 0):  # Down
            return 1 if x < 9 and state[x+1, y] == 0.5 else 0
        else:  # Up
            return 1 if x > 0 and state[x-1, y] == 0.5 else 0

    def _check_body_left(self, state: np.ndarray, head_pos: tuple) -> int:
        """Check if body is to the left of head"""
        x, y = head_pos
        if self.direction == (0, 1):  # Right
            return 1 if x > 0 and state[x-1, y] == 0.5 else 0
        elif self.direction == (0, -1):  # Left
            return 1 if x < 9 and state[x+1, y] == 0.5 else 0
        elif self.direction == (1, 0):  # Down
            return 1 if y > 0 and state[x, y-1] == 0.5 else 0
        else:  # Up
            return 1 if y < 9 and state[x, y+1] == 0.5 else 0

    def _check_body_right(self, state: np.ndarray, head_pos: tuple) -> int:
        """Check if body is to the right of head"""
        x, y = head_pos
        if self.direction == (0, 1):  # Right
            return 1 if x < 9 and state[x+1, y] == 0.5 else 0
        elif self.direction == (0, -1):  # Left
            return 1 if x > 0 and state[x-1, y] == 0.5 else 0
        elif self.direction == (1, 0):  # Down
            return 1 if y < 9 and state[x, y+1] == 0.5 else 0
        else:  # Up
            return 1 if y > 0 and state[x, y-1] == 0.5 else 0



    def _update_learning_parameters(self):
        """Update learning parameters (epsilon, learning rate)"""
        # Decay epsilon
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay

        # Update learning rate with scheduling
        if self.use_lr_scheduling and self.episodes_trained % self.lr_decay_interval == 0:
            self.learning_rate = max(self.lr_min, self.learning_rate * self.lr_decay_rate)

        self.episodes_trained += 1

    def save(self, path: str):
        """Save model with version compatibility"""
        save_data = {
            'version': 'v2',
            'q_table': self.q_table,
            'memory': list(self.memory),
            'last_actions': list(self.last_actions),
            'epsilon': self.epsilon,
            'learning_rate': self.learning_rate,
            'episodes_trained': self.episodes_trained,
            'training_metrics': self.training_metrics
        }

        # Create backup if auto backup is enabled
        if self.config.AUTO_BACKUP and os.path.exists(path):
            backup_path = path.replace('.npy', f'_backup_{self.episodes_trained}.npy')
            if os.path.exists(path):
                os.rename(path, backup_path)

        np.save(path, save_data)
        logging.info(f"Model saved to {path} (version v2)")

    def load(self, path: str):
        """Load model with version compatibility"""
        if not os.path.exists(path):
            logging.warning(f"Model file {path} not found")
            return False

        try:
            data = np.load(path, allow_pickle=True).item()

            # Check version compatibility
            model_version = data.get('version', 'v1')
            logging.info(f"Loading model version {model_version}")

            # Load Q-table with shape compatibility check
            loaded_q_table = data['q_table']
            if loaded_q_table.shape != self.q_table.shape:
                logging.warning(f"Q-table shape mismatch: {loaded_q_table.shape} vs {self.q_table.shape}")

                # Handle legacy models
                if loaded_q_table.shape == (10, 10, 4, 3, 3):
                    # Standard model format - load it
                    self.q_table = loaded_q_table
                    logging.info("Standard model loaded successfully")
                else:
                    logging.warning("Incompatible model format, starting fresh")
            else:
                self.q_table = loaded_q_table

            # Load other data
            self.memory = deque(data.get('memory', []), maxlen=self.config.MEMORY_SIZE)
            self.last_actions = deque(data.get('last_actions', []), maxlen=10)
            self.epsilon = data.get('epsilon', self.config.EPSILON_START)
            self.learning_rate = data.get('learning_rate', self.config.LEARNING_RATE)
            self.episodes_trained = data.get('episodes_trained', 0)

            # Load training metrics
            if 'training_metrics' in data:
                self.training_metrics = data['training_metrics']

            logging.info(f"Model loaded from {path} (version {model_version}, {len(self.memory)} experiences)")
            return True

        except Exception as e:
            logging.error(f"Error loading model from {path}: {e}")
            return False
