# Snake Reinforcement Learning Project

A complete snake game implementation with enhanced Q-Learning reinforcement learning and web interface for training and evaluation.

## 🎯 Key Features

- **Enhanced Q-Learning Agent**: Optimized Q-table with improved state representation and training algorithms
- **Advanced Training System**: Curriculum learning, early stopping, and learning rate scheduling
- **Interactive Web Dashboard**: Real-time training monitoring, model evaluation, and training control
- **Robust Model Management**: Automatic saving, loading, versioning, and backup system
- **Optimized Performance**: Improved reward system and exploration strategies
- **REST API**: Clean and reliable communication with comprehensive endpoints
- **Docker Support**: Easy deployment with single container setup

## 🚀 Performance Improvements

This enhanced version includes significant improvements over the original:

- **Better Learning**: Enhanced reward system with dynamic bonuses
- **Smarter Exploration**: Balanced action selection and optimized epsilon decay
- **Curriculum Learning**: Progressive difficulty stages for better training
- **Early Stopping**: Prevents overtraining and saves computational resources
- **Model Versioning**: Backward compatibility with automatic model migration
- **Increased Memory**: 10,000 experience buffer (2x larger)
- **Training Continuity**: Seamless training continuation after completion

## System Architecture

- **Backend**: Flask app serving both API and static files
- **Frontend**: Vanilla JavaScript with automatic status updates
- **Training**: Q-Learning agent with experience replay
- **Storage**: Models saved as NumPy files for persistence

## 🏁 Quick Start

### With Docker (Recommended)
```bash
cd snake-rl
docker compose up
```
Access web interface at `http://localhost:5000`

### Local Development
```bash
cd snake-rl
source venv/bin/activate
pip install flask numpy matplotlib
python app.py  # Main application
```
Access web interface at `http://localhost:5000`

### Command Line Training
```bash
cd snake-rl
source venv/bin/activate
python training.py  # Enhanced training with all features
```

## 📱 Usage

### Enhanced Web Interface
1. **Start Training**: Click "Start Training" to begin or continue training
2. **Stop Training**: Click "Stop Training" to pause training at any time
3. **Reset Training**: Click "Reset Training" to start fresh from episode 0
4. **Monitor Progress**: Real-time updates every 2 seconds showing:
   - Current episode and progress percentage
   - Latest score and 100-episode average
   - Exploration rate (epsilon)
   - Memory usage and training status
5. **Evaluate Model**: Click "Evaluate Model" to test the trained agent
6. **View Results**: Evaluation shows mean/max scores, steps, and action distribution

### Enhanced Training Behavior
- **Smart Loading**: Automatically detects and loads the best available model (v2 → final → legacy)
- **Continuous Training**: Training continues beyond initial episode limit
- **Auto-Save**: Models saved every 50 episodes with automatic backup system
- **Early Stopping**: Training stops automatically when no improvement is detected
- **Curriculum Learning**: Progressive difficulty stages for optimal learning
- **Model Versioning**: Enhanced models (v2) with backward compatibility

## 📁 Project Structure

```
snake-rl/
├── web/                   # Web interface assets
│   └── static/            # HTML, CSS, JavaScript
├── app.py                 # Main Flask application
├── snake_game.py          # Core game logic
├── agent.py               # Q-Learning agent
├── training.py            # Command-line training
├── evaluation.py          # Model evaluation
├── config.py              # Centralized configuration
├── cleanup_backups.py     # Backup cleanup utility
├── Dockerfile             # Docker configuration
├── docker-compose.yml     # Docker Compose setup
├── requirements.txt       # Python dependencies
└── models/                # Saved models (auto-created)
    └── snake_agent_v2.npy      # Current model
```

## ⚙️ Configuration

All parameters are centralized in `config.py` for easy tuning:

### Training Parameters
```python
EPISODES = 1000           # Training episodes per session
BATCH_SIZE = 32           # Experience replay batch size
LEARNING_RATE = 0.001     # Q-learning rate
MEMORY_SIZE = 10000       # Experience buffer size
EPSILON_START = 1.0       # Initial exploration rate
EPSILON_MIN = 0.01        # Minimum exploration rate
EPSILON_DECAY = 0.995     # Exploration decay rate
```

### Features
```python
# Learning Rate Scheduling
USE_LR_SCHEDULING = True
LR_DECAY_RATE = 0.99
LR_MIN = 0.0001

# Early Stopping
USE_EARLY_STOPPING = True
EARLY_STOP_PATIENCE = 100

# Dynamic Rewards
USE_DYNAMIC_REWARDS = True
REWARD_FOOD = 100           # Food reward
REWARD_DEATH = -100         # Death penalty
REWARD_STEP = -1            # Step penalty
```

## 🌐 API Endpoints

### Training Control
- `GET /` - Web interface
- `POST /api/training/start` - Start training
- `POST /api/training/stop` - Stop training
- `POST /api/training/reset` - Reset training to episode 0
- `GET /api/training/status` - Get training status

### Model Evaluation
- `GET /api/evaluation` - Evaluate trained model
- `GET /api/final_evaluation` - Evaluation (compatibility endpoint)
- `GET /api/status` - System status

### Utilities
- `GET /api/test` - API health check

## 🔬 Technical Details

### State Representation
- **Q-table**: 5D structure (10×10×4×3×3 = 3,600 states)
- **Grid Size**: 10×10 game board
- **Action Space**: 3 actions (turn left, turn right, go straight)

### Reward System
- **Food Reward**: +100
- **Death Penalty**: -100
- **Step Penalty**: -1 (encourages efficiency)
- **Dynamic Rewards**: Proximity and survival bonuses
- **Length Multiplier**: Food reward scales with snake length

### Memory & Learning
- **Experience Buffer**: 10,000 transitions
- **Batch Training**: 32 experiences per update
- **Learning Rate**: Adaptive with scheduling (0.001 → 0.0001)
- **Exploration**: Epsilon decay (0.995) with balanced action selection
- **Persistence**: Q-table, memory, and training metrics saved

### Features
- **Early Stopping**: Automatic training termination
- **Backup System**: Automatic model backups every 500 episodes
- **Performance Tracking**: Episode metrics and progress monitoring

## 📊 Performance Results

### Training Improvements
- **Score Achievement**: Consistently reaching scores of 2+ (vs. 0-1 in original)
- **Training Stability**: No crashes or memory issues during extended training
- **Learning Efficiency**: Early stopping prevents overtraining
- **Memory Utilization**: Full 10,000 experience buffer utilization

### Typical Training Session
```
Episode 50   | Score: 2 | Avg: 0.8 | Epsilon: 0.60
Episode 100  | Score: 2 | Avg: 1.1 | Epsilon: 0.37
Episode 200  | Score: 1 | Avg: 1.2 | Epsilon: 0.13
Episode 300  | Score: 2 | Avg: 1.4 | Epsilon: 0.05
Training completed with early stopping
```

## 🛠️ Troubleshooting

### Common Issues

**Training won't start after completion**
```bash
# Reset training to start fresh
curl -X POST http://localhost:5000/api/training/reset
```

**Model compatibility errors**
```bash
# Remove old models and restart training
rm -f models/snake_agent*.npy
python training.py
```

**Web interface not loading**
```bash
# Check if web app is running
curl http://localhost:5000/api/status
# Restart web app
python app.py
```

### Development Tips
- Use Docker Compose for the most reliable deployment
- Check logs for detailed training information
- Models are automatically saved in `models/` directory
- Use `training.py` for command-line training without web interface

## 🎯 Future Enhancements

Potential improvements for advanced users:
- **Enhanced State Representation**: 8D state space (currently disabled for stability)
- **Prioritized Experience Replay**: Advanced sampling strategy
- **Double DQN**: Reduced overestimation bias
- **Dueling DQN**: Separate value and advantage estimation
- **Live Game Visualization**: Real-time snake game rendering
- **Multi-Agent Training**: Competitive snake environments
