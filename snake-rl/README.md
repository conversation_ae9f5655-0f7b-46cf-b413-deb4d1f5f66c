# Snake Reinforcement Learning Project

A complete snake game implementation with enhanced Q-Learning reinforcement learning and web interface for training and evaluation.

## 🎯 Key Features

- **Enhanced Q-Learning Agent**: Optimized Q-table with improved state representation and training algorithms
- **Advanced Training System**: Curriculum learning, early stopping, and learning rate scheduling
- **Interactive Web Dashboard**: Real-time training monitoring, model evaluation, and training control
- **Robust Model Management**: Automatic saving, loading, versioning, and backup system
- **Optimized Performance**: Improved reward system and exploration strategies
- **REST API**: Clean and reliable communication with comprehensive endpoints
- **Docker Support**: Easy deployment with single container setup
- **Persistent State Management**: Complete episode counting, high score tracking, and model status persistence

## 🚀 Performance Improvements

This enhanced version includes significant improvements over the original:

- **Better Learning**: Enhanced reward system with dynamic bonuses
- **Smarter Exploration**: Balanced action selection and optimized epsilon decay
- **Curriculum Learning**: Progressive difficulty stages for better training
- **Early Stopping**: Prevents overtraining and saves computational resources
- **Model Versioning**: Backward compatibility with automatic model migration
- **Increased Memory**: 10,000 experience buffer (2x larger)
- **Training Continuity**: Seamless training continuation after completion
- **Accurate Episode Tracking**: Fixed episode counting logic for consistent progress tracking
- **Complete State Persistence**: High scores, training metrics, and model status preserved across restarts

## System Architecture

- **Backend**: Flask app serving both API and static files
- **Frontend**: Vanilla JavaScript with automatic status updates
- **Training**: Q-Learning agent with experience replay
- **Storage**: Models saved as NumPy files for persistence

## 🏁 Quick Start

### With Docker (Recommended)
```bash
cd snake-rl
docker compose up
```
Access web interface at `http://localhost:5000`

### Local Development
```bash
cd snake-rl
source venv/bin/activate
pip install flask numpy matplotlib
python app.py  # Main application
```
Access web interface at `http://localhost:5000`

### Command Line Training
```bash
cd snake-rl
source venv/bin/activate
python training.py  # Enhanced training with all features
```

## 📱 Usage

### Enhanced Web Interface
1. **Start Training**: Click "Start Training" to begin or continue training
2. **Stop Training**: Click "Stop Training" to pause training at any time
3. **Reset Training**: Click "Reset Training" to start fresh from episode 0
4. **Monitor Progress**: Real-time updates every 2 seconds showing:
   - Current episode and total episodes trained
   - Latest score and rolling average
   - High score (persistent across restarts)
   - Exploration rate (epsilon)
   - Memory usage and training status
   - Model loaded status (Fresh Start vs. Model Path)
5. **Evaluate Model**: Click "Evaluate Model" to test the trained agent
6. **View Results**: Evaluation shows mean/max scores, steps, and action distribution

### Enhanced Training Behavior
- **Smart Loading**: Automatically detects and loads the best available model
- **Continuous Training**: Training continues beyond initial episode limit
- **Auto-Save**: Models saved every 50 episodes with automatic backup system
- **Episode Tracking**: Accurate episode counting with session and total episode separation
- **State Persistence**: High scores, training metrics, and model status preserved across Docker restarts
- **Model Status Tracking**: Clear indication of whether starting fresh or continuing from saved model

## 📁 Project Structure

```
snake-rl/
├── web/                   # Web interface assets
│   └── static/            # HTML, CSS, JavaScript
├── app.py                 # Main Flask application
├── snake_game.py          # Core game logic
├── agent.py               # Q-Learning agent
├── training.py            # Command-line training
├── evaluation.py          # Model evaluation
├── config.py              # Centralized configuration
├── cleanup_backups.py     # Backup cleanup utility
├── Dockerfile             # Docker configuration
├── docker-compose.yml     # Docker Compose setup
├── requirements.txt       # Python dependencies
└── models/                # Saved models (auto-created)
    └── snake_agent_v2.npy      # Current model
```

## ⚙️ Configuration

All parameters are centralized in `config.py` for easy tuning:

### Training Parameters
```python
EPISODES = 1000           # Training episodes per session
BATCH_SIZE = 32           # Experience replay batch size
LEARNING_RATE = 0.001     # Q-learning rate
MEMORY_SIZE = 10000       # Experience buffer size
EPSILON_START = 1.0       # Initial exploration rate
EPSILON_MIN = 0.01        # Minimum exploration rate
EPSILON_DECAY = 0.995     # Exploration decay rate
```

### Features
```python
# Learning Rate Scheduling
USE_LR_SCHEDULING = True
LR_DECAY_RATE = 0.99
LR_MIN = 0.0001

# Early Stopping
USE_EARLY_STOPPING = True
EARLY_STOP_PATIENCE = 100

# Dynamic Rewards
USE_DYNAMIC_REWARDS = True
REWARD_FOOD = 100           # Food reward
REWARD_DEATH = -100         # Death penalty
REWARD_STEP = -1            # Step penalty
```

## 🌐 API Endpoints

### Training Control
- `GET /` - Web interface
- `POST /api/training/start` - Start training
- `POST /api/training/stop` - Stop training
- `POST /api/training/reset` - Reset training to episode 0 (deletes saved model)
- `GET /api/training/status` - Get comprehensive training status

### Model Evaluation
- `GET /api/evaluation` - Evaluate trained model
- `GET /api/final_evaluation` - Evaluation (compatibility endpoint)
- `GET /api/status` - System status and model information

### Utilities
- `GET /api/test` - API health check

### Training Status Response
The `/api/training/status` endpoint returns comprehensive information:
```json
{
  "running": false,
  "episode": 0,
  "total_episodes": 386,
  "episodes_trained": 386,
  "score": 0,
  "avg_score": 0.0,
  "high_score": 3,
  "epsilon": 0.01,
  "memory_size": 10000,
  "completed": false,
  "model_loaded_from": "models/snake_agent_v2.npy"
}
```

## 🔬 Technical Details

### State Representation
- **Q-table**: 5D structure (10×10×4×3×3 = 3,600 states)
- **Grid Size**: 10×10 game board
- **Action Space**: 3 actions (turn left, turn right, go straight)

### Reward System
- **Food Reward**: +100
- **Death Penalty**: -100
- **Step Penalty**: -1 (encourages efficiency)
- **Dynamic Rewards**: Proximity and survival bonuses
- **Length Multiplier**: Food reward scales with snake length

### Memory & Learning
- **Experience Buffer**: 10,000 transitions
- **Batch Training**: 32 experiences per update
- **Learning Rate**: Adaptive with scheduling (0.001 → 0.0001)
- **Exploration**: Epsilon decay (0.995) with balanced action selection
- **Persistence**: Q-table, memory, training metrics, and high scores saved

### Enhanced Features
- **Episode Tracking**: Separate tracking of session episodes and total trained episodes
- **High Score Persistence**: High scores saved in model and restored on load
- **Model Status Tracking**: Clear indication of fresh start vs. loaded model
- **Backup System**: Automatic model backups with episode numbers
- **Performance Tracking**: Comprehensive metrics and progress monitoring
- **State Consistency**: All training state preserved across Docker restarts

## 📊 Performance Results

### Training Improvements
- **Score Achievement**: Consistently reaching scores of 3+ with proper training
- **Training Stability**: No crashes or memory issues during extended training
- **Episode Tracking**: Accurate episode counting with persistent state
- **Memory Utilization**: Full 10,000 experience buffer utilization
- **State Persistence**: Complete training state preserved across restarts

### Typical Training Session
```
Fresh Start:
Episode 50   | Score: 1 | Avg: 0.8 | High: 2 | Epsilon: 0.60
Episode 100  | Score: 2 | Avg: 1.1 | High: 3 | Epsilon: 0.37
Episode 200  | Score: 1 | Avg: 1.2 | High: 3 | Epsilon: 0.13
Episode 300  | Score: 3 | Avg: 1.4 | High: 3 | Epsilon: 0.05

After Docker Restart (Model Loaded):
Episodes Trained: 300 | High Score: 3 | Model: snake_agent_v2.npy
Continue Training: Episode 301+ with preserved state
```

## 🛠️ Troubleshooting

### Common Issues

**Episode counting appears incorrect**
```bash
# Check training status for detailed episode information
curl http://localhost:5000/api/training/status
# Shows: episode (current session), episodes_trained (from model), total_episodes (sum)
```

**High score not showing in web interface**
```bash
# Refresh browser cache
Ctrl+F5 (Windows/Linux) or Cmd+Shift+R (Mac)
# Check API directly
curl http://localhost:5000/api/training/status | grep high_score
```

**Model status shows "fresh_start" after training**
```bash
# This is normal during training - status updates after training completes
# Check if model file exists
ls -la models/snake_agent_v2.npy
```

**Training won't start after completion**
```bash
# Reset training to start fresh (deletes saved model)
curl -X POST http://localhost:5000/api/training/reset
```

**Web interface not loading**
```bash
# Check if web app is running
curl http://localhost:5000/api/status
# Restart with Docker
docker compose restart
```

### Development Tips
- Use Docker Compose for the most reliable deployment
- Episode counting: `total_episodes = episodes_trained + current_episode`
- High scores are automatically saved in model and restored on load
- Model status updates automatically when training saves a model
- All training state persists across Docker restarts

## 🔧 Recent Fixes & Improvements

### Episode Counting System (Fixed)
- **Problem**: Episode counting was inconsistent due to multiple increments per episode
- **Solution**: Removed duplicate episode counting from replay function
- **Result**: Accurate episode tracking with clear separation of session vs. total episodes

### High Score Persistence (Fixed)
- **Problem**: High scores were not properly saved and restored from models
- **Solution**: Enhanced model saving/loading to include training metrics
- **Result**: High scores persist across Docker restarts and model loads

### Model Status Tracking (Fixed)
- **Problem**: Web interface showed "fresh_start" even after loading existing models
- **Solution**: Added dynamic model status checking and updating
- **Result**: Accurate model status display (Fresh Start vs. Model Path)

### Web Interface Data Display (Fixed)
- **Problem**: Web interface didn't show model data when training wasn't active
- **Solution**: Updated frontend to display stats in all states (Ready, Training, Complete)
- **Result**: Complete model information always visible in web interface

## 🎯 Future Enhancements

Potential improvements for advanced users:
- **Enhanced State Representation**: 8D state space (currently disabled for stability)
- **Prioritized Experience Replay**: Advanced sampling strategy
- **Double DQN**: Reduced overestimation bias
- **Dueling DQN**: Separate value and advantage estimation
- **Live Game Visualization**: Real-time snake game rendering
- **Multi-Agent Training**: Competitive snake environments
